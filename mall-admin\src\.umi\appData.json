{"cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "pkg": {"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.8.1", "@ant-design/pro-components": "^2.6.48", "@ant-design/pro-table": "^3.19.0", "@umijs/route-utils": "^2.2.2", "antd": "^5.13.2", "antd-style": "^3.6.1", "classnames": "^2.5.1", "lodash": "^4.17.21", "moment": "^2.30.1", "omit.js": "^2.0.2", "querystring": "^0.2.1", "rc-menu": "^9.12.4", "rc-util": "^5.38.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/express": "^4.17.21", "@types/history": "^4.7.11", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-helmet": "^6.1.11", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.1.1", "@umijs/max": "^4.1.1", "cross-env": "^7.0.3", "eslint": "^8.56.0", "express": "^4.18.2", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "prettier": "^2.8.8", "react-dev-inspector": "^1.9.0", "swagger-ui-dist": "^4.19.1", "ts-node": "^10.9.2", "typescript": "^5.3.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}, "pkgPath": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\package.json", "plugins": {"./node_modules/.pnpm/@umijs+core@4.4.11/node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+core@4.4.11/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+core@4.4.11/node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 30}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 17}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/preset.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/preset", "key": "preset"}, "umi-presets-pro": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 6}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/umi-presets-pro/dist/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "umi-presets-pro", "key": "umiPresetsPro"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+did-you-know@1.0.3/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [32]}, "register": 34}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 20}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\react", "react-dom": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\react-dom", "react-router": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\react-router@6.3.0_react@18.3.1\\node_modules\\react-router", "react-router-dom": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\react-router-dom@6.3.0_reac_beb70b932a60871592d5dec302f52003\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 99}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 187}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 32}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 52}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [2]}, "register": 6}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 12}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 29}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 7}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 49}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [26], "onStart": [0]}, "register": 77}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 28}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+preset-umi@4.4.11_@t_635e4615840fc79c5685c396393d3016/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugin-run@4.4.11/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/access.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/access", "key": "access"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/analytics", "key": "analytics"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [3], "modifyAppData": [0]}, "register": 9}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/antd.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 140}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/dva.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/dva", "key": "dva"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {"modifyConfig": [0], "addLayouts": [0], "modifyAppData": [1]}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/layout.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/layout", "key": "layout"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/locale.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/locale", "key": "locale"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/mf.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/mf", "key": "mf"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/model.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun", "key": "qiankun"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/react-query", "key": "reactQuery"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/request.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/request", "key": "request"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+plugins@4.4.11_@babe_ff6cd474bf51857cef344fb90cf76589/node_modules/@umijs/plugins/dist/valtio", "key": "valtio"}, "./node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "./node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+max@4.4.11_@babel+co_4a10b776480c98d83a663814e947a557/node_modules/@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/.pnpm/umi-presets-pro@2.0.3_@babe_da659e1d96020f58e03fbb1ad848ea38/node_modules/umi-presets-pro/dist/features/proconfig": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/umi-presets-pro@2.0.3_@babe_da659e1d96020f58e03fbb1ad848ea38/node_modules/umi-presets-pro/dist/features/proconfig.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/umi-presets-pro@2.0.3_@babe_da659e1d96020f58e03fbb1ad848ea38/node_modules/umi-presets-pro/dist/features/proconfig", "key": "proconfig"}, "./node_modules/.pnpm/umi-presets-pro@2.0.3_@babe_da659e1d96020f58e03fbb1ad848ea38/node_modules/umi-presets-pro/dist/features/maxtabs": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/umi-presets-pro@2.0.3_@babe_da659e1d96020f58e03fbb1ad848ea38/node_modules/umi-presets-pro/dist/features/maxtabs.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/umi-presets-pro@2.0.3_@babe_da659e1d96020f58e03fbb1ad848ea38/node_modules/umi-presets-pro/dist/features/maxtabs", "key": "maxtabs"}, "@umijs/max-plugin-openapi": {"config": {}, "time": {"hooks": {"modifyRoutes": [0], "onStart": [1]}, "register": 350}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+max-plugin-openapi@2_52c072b902c2e1ab24ba8ba4161ddef4/node_modules/@umijs/max-plugin-openapi/dist/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "@umijs/max-plugin-openapi", "key": "openAPI"}, "./node_modules/.pnpm/@alita+plugins@3.5.4_@babel_5ec18335ad825f579d26545cd7b314f5/node_modules/@alita/plugins/dist/keepalive": {"config": {}, "time": {"hooks": {}, "register": 50}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@alita+plugins@3.5.4_@babel_5ec18335ad825f579d26545cd7b314f5/node_modules/@alita/plugins/dist/keepalive.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@alita+plugins@3.5.4_@babel_5ec18335ad825f579d26545cd7b314f5/node_modules/@alita/plugins/dist/keepalive", "key": "keepalive"}, "./node_modules/.pnpm/@alita+plugins@3.5.4_@babel_5ec18335ad825f579d26545cd7b314f5/node_modules/@alita/plugins/dist/tabs-layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@alita+plugins@3.5.4_@babel_5ec18335ad825f579d26545cd7b314f5/node_modules/@alita/plugins/dist/tabs-layout.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@alita+plugins@3.5.4_@babel_5ec18335ad825f579d26545cd7b314f5/node_modules/@alita/plugins/dist/tabs-layout", "key": "tabsLayout"}, "@umijs/request-record": {"config": {"default": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}}, "time": {"hooks": {"modifyConfig": [0], "modifyAppData": [0]}, "register": 77}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+request-record@1.1.4_13f316c4199e003c00e63b8f02a1185c/node_modules/@umijs/request-record/dist/cjs/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "@umijs/request-record", "key": "requestRecord"}, "./node_modules/.pnpm/@umijs+core@4.4.11/node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+core@4.4.11/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin", "id": "./node_modules/.pnpm/@umijs+core@4.4.11/node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"hash": true, "routes": [{"path": "/user", "layout": false, "routes": [{"name": "login", "path": "/user/login", "component": "./User/Login"}]}, {"path": "/welcome", "name": "welcome", "icon": "smile", "component": "./Welcome"}, {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "sub-page", "component": "./Admin"}]}, {"name": "list.table-list", "icon": "table", "path": "/list", "component": "./TableList"}, {"path": "/product", "name": "product", "icon": "table", "routes": [{"path": "/product/list", "name": "productList", "component": "./ProductList"}]}, {"path": "/", "redirect": "/welcome"}, {"path": "/order", "name": "order", "icon": "shoppingCart", "routes": [{"path": "/order/list", "name": "list", "component": "./OrderList"}, {"path": "/order/detail/:id", "name": "detail", "component": "./OrderDetail", "hideInMenu": true}]}, {"path": "*", "layout": false, "component": "./404"}], "theme": {"root-entry-name": "variable"}, "ignoreMomentLocale": true, "proxy": {"/api/": {"target": "http://localhost:8080", "changeOrigin": true}}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Ant Design Pro", "layout": {"locale": true, "navTheme": "light", "colorPrimary": "#1890ff", "layout": "mix", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "Ant Design Pro", "pwa": true, "logo": "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg", "iconfontUrl": "", "token": {}}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "zh-CN", "antd": true, "baseNavigator": true}, "antd": {}, "request": {}, "access": {}, "headScripts": [{"src": "/scripts/loading.js", "async": true}], "presets": ["umi-presets-pro"], "openAPI": [{"requestLibPath": "import { request } from '@umijs/max'", "schemaPath": "http://localhost:8080/v3/api-docs", "projectName": "swagger"}], "mfsu": {"strategy": "normal"}, "esbuildMinifyIIFE": true, "requestRecord": {}}, "mainConfigFile": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\config\\config.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": {"strategy": "normal"}, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\react", "react-dom": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\react-dom", "react-router": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\react-router@6.3.0_react@18.3.1\\node_modules\\react-router", "react-router-dom": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\react-router-dom@6.3.0_reac_beb70b932a60871592d5dec302f52003\\node_modules\\react-router-dom", "@": "C:/Users/<USER>/Desktop/mall/mall-admin/src", "@@": "C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi", "regenerator-runtime": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\regenerator-runtime@0.13.11\\node_modules\\regenerator-runtime", "antd": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\antd", "moment": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\dayjs@1.11.13\\node_modules\\dayjs", "@umijs/max": "@@/exports"}, "requestRecord": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}, "hash": true, "routes": [{"path": "/user", "layout": false, "routes": [{"name": "login", "path": "/user/login", "component": "./User/Login"}]}, {"path": "/welcome", "name": "welcome", "icon": "smile", "component": "./Welcome"}, {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "sub-page", "component": "./Admin"}]}, {"name": "list.table-list", "icon": "table", "path": "/list", "component": "./TableList"}, {"path": "/product", "name": "product", "icon": "table", "routes": [{"path": "/product/list", "name": "productList", "component": "./ProductList"}]}, {"path": "/", "redirect": "/welcome"}, {"path": "/order", "name": "order", "icon": "shoppingCart", "routes": [{"path": "/order/list", "name": "list", "component": "./OrderList"}, {"path": "/order/detail/:id", "name": "detail", "component": "./OrderDetail", "hideInMenu": true}]}, {"path": "*", "layout": false, "component": "./404"}], "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#1890ff", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626", "root-entry-name": "variable"}, "proxy": {"/api/": {"target": "http://localhost:8080", "changeOrigin": true}}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Ant Design Pro", "layout": {"locale": true, "navTheme": "light", "colorPrimary": "#1890ff", "layout": "mix", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "Ant Design Pro", "pwa": true, "logo": "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg", "iconfontUrl": "", "token": {}}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "zh-CN", "antd": true, "baseNavigator": true}, "antd": {}, "request": {}, "access": {}, "headScripts": [{"src": "/scripts/loading.js", "async": true}], "presets": ["umi-presets-pro"], "openAPI": [{"requestLibPath": "import { request } from '@umijs/max'", "schemaPath": "http://localhost:8080/v3/api-docs", "projectName": "swagger"}], "esbuildMinifyIIFE": true, "targets": {"chrome": 80}, "define": {"ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION": "", "REACT_APP_ENV": "dev"}}, "routes": {"1": {"path": "/user", "layout": false, "id": "1", "absPath": "/user"}, "2": {"name": "login", "path": "/user/login", "file": "@/pages/User/Login/index.tsx", "parentId": "1", "id": "2", "absPath": "/user/login", "__content": "import { Footer } from '@/components';\nimport { login } from '@/services/swagger/authController';\nimport { getFakeCaptcha } from '@/services/ant-design-pro/login';\nimport {\n  AlipayCircleOutlined,\n  LockOutlined,\n  MobileOutlined,\n  TaobaoCircleOutlined,\n  UserOutlined,\n  WeiboCircleOutlined,\n} from '@ant-design/icons';\nimport {\n  LoginForm,\n  ProFormCaptcha,\n  ProFormCheckbox,\n  ProFormText,\n} from '@ant-design/pro-components';\nimport { FormattedMessage, history, SelectLang, useIntl, useModel, Helmet } from '@umijs/max';\nimport { message, Tabs } from 'antd';\nimport Settings from '../../../../config/defaultSettings';\nimport React, { useState } from 'react';\nimport { flushSync } from 'react-dom';\nimport { createStyles } from 'antd-style';\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    action: {\n      marginLeft: '8px',\n      color: 'rgba(0, 0, 0, 0.2)',\n      fontSize: '24px',\n      verticalAlign: 'middle',\n      cursor: 'pointer',\n      transition: 'color 0.3s',\n      '&:hover': {\n        color: token.colorPrimaryActive,\n      },\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n  };\n});\n\nconst ActionIcons = () => {\n  const { styles } = useStyles();\n\n  return (\n    <>\n      <AlipayCircleOutlined key=\"AlipayCircleOutlined\" className={styles.action} />\n      <TaobaoCircleOutlined key=\"TaobaoCircleOutlined\" className={styles.action} />\n      <WeiboCircleOutlined key=\"WeiboCircleOutlined\" className={styles.action} />\n    </>\n  );\n};\n\nconst Lang = () => {\n  const { styles } = useStyles();\n\n  return (\n    <div className={styles.lang} data-lang>\n      {SelectLang && <SelectLang />}\n    </div>\n  );\n};\n\nconst Login: React.FC = () => {\n  const [type, setType] = useState<string>('account');\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const { styles } = useStyles();\n  const intl = useIntl();\n\n  const fetchUserInfo = async () => {\n    const userInfo = await initialState?.fetchUserInfo?.();\n    if (userInfo) {\n      flushSync(() => {\n        setInitialState((s) => ({\n          ...s,\n          currentUser: userInfo,\n        }));\n      });\n    }\n  };\n\n  const handleSubmit = async (values: API.LoginRequest) => {\n    try {\n      // 登录\n      const msg = await login({ ...values });\n      if (msg) { // 假设返回的string不为空则表示成功\n        const defaultLoginSuccessMessage = intl.formatMessage({\n          id: 'pages.login.success',\n          defaultMessage: '登录成功！',\n        });\n        message.success(defaultLoginSuccessMessage);\n        localStorage.setItem('username', values.username || ''); // 存储用户名\n        localStorage.setItem('token', msg); // 存储token\n        await fetchUserInfo();\n        const urlParams = new URL(window.location.href).searchParams;\n        history.push(urlParams.get('redirect') || '/');\n        return;\n      }\n      message.error(intl.formatMessage({\n        id: 'pages.login.failure',\n        defaultMessage: '登录失败，请重试！',\n      }));\n      console.log(msg);\n    } catch (error) {\n      const defaultLoginFailureMessage = intl.formatMessage({\n        id: 'pages.login.failure',\n        defaultMessage: '登录失败，请重试！',\n      });\n      console.log(error);\n      message.error(defaultLoginFailureMessage);\n    }\n  };\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          {intl.formatMessage({\n            id: 'menu.login',\n            defaultMessage: '登录页',\n          })}\n          - {Settings.title}\n        </title>\n      </Helmet>\n      <Lang />\n      <div\n        style={{\n          flex: '1',\n          padding: '32px 0',\n        }}\n      >\n        <LoginForm\n          contentStyle={{\n            minWidth: 280,\n            maxWidth: '75vw',\n          }}\n          logo={<img alt=\"logo\" src=\"/logo.svg\" />}\n          title=\"Ant Design\"\n          subTitle={intl.formatMessage({ id: 'pages.layouts.userLayout.title' })}\n          initialValues={{\n            autoLogin: true,\n          }}\n          actions={[\n            <FormattedMessage\n              key=\"loginWith\"\n              id=\"pages.login.loginWith\"\n              defaultMessage=\"其他登录方式\"\n            />,\n            <ActionIcons key=\"icons\" />,\n          ]}\n          onFinish={async (values) => {\n            await handleSubmit(values as API.LoginRequest);\n          }}\n        >\n          <Tabs\n            activeKey={type}\n            onChange={setType}\n            centered\n            items={[\n              {\n                key: 'account',\n                label: intl.formatMessage({\n                  id: 'pages.login.accountLogin.tab',\n                  defaultMessage: '账户密码登录',\n                }),\n              },\n              {\n                key: 'mobile',\n                label: intl.formatMessage({\n                  id: 'pages.login.phoneLogin.tab',\n                  defaultMessage: '手机号登录',\n                }),\n              },\n            ]}\n          />\n\n          {type === 'account' && (\n            <>\n              <ProFormText\n                name=\"username\"\n                fieldProps={{\n                  size: 'large',\n                  prefix: <UserOutlined />,\n                }}\n                placeholder={intl.formatMessage({\n                  id: 'pages.login.username.placeholder',\n                  defaultMessage: '用户名: admin or user',\n                })}\n                rules={[\n                  {\n                    required: true,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.username.required\"\n                        defaultMessage=\"请输入用户名!\"\n                      />\n                    ),\n                  },\n                ]}\n              />\n              <ProFormText.Password\n                name=\"password\"\n                fieldProps={{\n                  size: 'large',\n                  prefix: <LockOutlined />,\n                }}\n                placeholder={intl.formatMessage({\n                  id: 'pages.login.password.placeholder',\n                  defaultMessage: '密码: ant.design',\n                })}\n                rules={[\n                  {\n                    required: true,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.password.required\"\n                        defaultMessage=\"请输入密码！\"\n                      />\n                    ),\n                  },\n                ]}\n              />\n            </>\n          )}\n\n          {type === 'mobile' && (\n            <>\n              <ProFormText\n                fieldProps={{\n                  size: 'large',\n                  prefix: <MobileOutlined />,\n                }}\n                name=\"mobile\"\n                placeholder={intl.formatMessage({\n                  id: 'pages.login.phoneNumber.placeholder',\n                  defaultMessage: '手机号',\n                })}\n                rules={[\n                  {\n                    required: true,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.phoneNumber.required\"\n                        defaultMessage=\"请输入手机号！\"\n                      />\n                    ),\n                  },\n                  {\n                    pattern: /^1\\d{10}$/,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.phoneNumber.invalid\"\n                        defaultMessage=\"手机号格式错误！\"\n                      />\n                    ),\n                  },\n                ]}\n              />\n              <ProFormCaptcha\n                fieldProps={{\n                  size: 'large',\n                  prefix: <LockOutlined />,\n                }}\n                captchaProps={{\n                  size: 'large',\n                }}\n                placeholder={intl.formatMessage({\n                  id: 'pages.login.captcha.placeholder',\n                  defaultMessage: '请输入验证码',\n                })}\n                captchaTextRender={(timing, count) => {\n                  if (timing) {\n                    return `${count} ${intl.formatMessage({\n                      id: 'pages.getCaptchaSecondText',\n                      defaultMessage: '获取验证码',\n                    })}`;\n                  }\n                  return intl.formatMessage({\n                    id: 'pages.login.phoneLogin.getVerificationCode',\n                    defaultMessage: '获取验证码',\n                  });\n                }}\n                name=\"captcha\"\n                rules={[\n                  {\n                    required: true,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.captcha.required\"\n                        defaultMessage=\"请输入验证码！\"\n                      />\n                    ),\n                  },\n                ]}\n                onGetCaptcha={async (phone) => {\n                  const result = await getFakeCaptcha({\n                    phone,\n                  });\n                  if (!result) {\n                    return;\n                  }\n                  message.success('获取验证码成功！验证码为：1234');\n                }}\n              />\n            </>\n          )}\n          <div\n            style={{\n              marginBottom: 24,\n            }}\n          >\n            <ProFormCheckbox noStyle name=\"autoLogin\">\n              <FormattedMessage id=\"pages.login.rememberMe\" defaultMessage=\"自动登录\" />\n            </ProFormCheckbox>\n            <a\n              style={{\n                float: 'right',\n              }}\n            >\n              <FormattedMessage id=\"pages.login.forgotPassword\" defaultMessage=\"忘记密码\" />\n            </a>\n          </div>\n        </LoginForm>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Login;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/User/Login/index.tsx"}, "3": {"path": "/welcome", "name": "welcome", "icon": "smile", "file": "@/pages/Welcome.tsx", "parentId": "ant-design-pro-layout", "id": "3", "absPath": "/welcome", "__content": "import { PageContainer } from '@ant-design/pro-components';\r\nimport { useModel } from '@umijs/max';\r\nimport { Card, theme } from 'antd';\r\nimport React from 'react';\r\n\r\n/**\r\n * 每个单独的卡片，为了复用样式抽成了组件\r\n * @param param0\r\n * @returns\r\n */\r\nconst InfoCard: React.FC<{\r\n  title: string;\r\n  index: number;\r\n  desc: string;\r\n  href: string;\r\n}> = ({ title, href, index, desc }) => {\r\n  const { useToken } = theme;\r\n\r\n  const { token } = useToken();\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        backgroundColor: token.colorBgContainer,\r\n        boxShadow: token.boxShadow,\r\n        borderRadius: '8px',\r\n        fontSize: '14px',\r\n        color: token.colorTextSecondary,\r\n        lineHeight: '22px',\r\n        padding: '16px 19px',\r\n        minWidth: '220px',\r\n        flex: 1,\r\n      }}\r\n    >\r\n      <div\r\n        style={{\r\n          display: 'flex',\r\n          gap: '4px',\r\n          alignItems: 'center',\r\n        }}\r\n      >\r\n        <div\r\n          style={{\r\n            width: 48,\r\n            height: 48,\r\n            lineHeight: '22px',\r\n            backgroundSize: '100%',\r\n            textAlign: 'center',\r\n            padding: '8px 16px 16px 12px',\r\n            color: '#FFF',\r\n            fontWeight: 'bold',\r\n            backgroundImage:\r\n              \"url('https://gw.alipayobjects.com/zos/bmw-prod/daaf8d50-8e6d-4251-905d-676a24ddfa12.svg')\",\r\n          }}\r\n        >\r\n          {index}\r\n        </div>\r\n        <div\r\n          style={{\r\n            fontSize: '16px',\r\n            color: token.colorText,\r\n            paddingBottom: 8,\r\n          }}\r\n        >\r\n          {title}\r\n        </div>\r\n      </div>\r\n      <div\r\n        style={{\r\n          fontSize: '14px',\r\n          color: token.colorTextSecondary,\r\n          textAlign: 'justify',\r\n          lineHeight: '22px',\r\n          marginBottom: 8,\r\n        }}\r\n      >\r\n        {desc}\r\n      </div>\r\n      <a href={href} target=\"_blank\" rel=\"noreferrer\">\r\n        了解更多 {'>'}\r\n      </a>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Welcome: React.FC = () => {\r\n  const { token } = theme.useToken();\r\n  const { initialState } = useModel('@@initialState');\r\n  return (\r\n    <PageContainer>\r\n      <Card\r\n        style={{\r\n          borderRadius: 8,\r\n        }}\r\n        bodyStyle={{\r\n          backgroundImage:\r\n            initialState?.settings?.navTheme === 'realDark'\r\n              ? 'background-image: linear-gradient(75deg, #1A1B1F 0%, #191C1F 100%)'\r\n              : 'background-image: linear-gradient(75deg, #FBFDFF 0%, #F5F7FF 100%)',\r\n        }}\r\n      >\r\n        <div\r\n          style={{\r\n            backgroundPosition: '100% -30%',\r\n            backgroundRepeat: 'no-repeat',\r\n            backgroundSize: '274px auto',\r\n            backgroundImage:\r\n              \"url('https://gw.alipayobjects.com/mdn/rms_a9745b/afts/img/A*BuFmQqsB2iAAAAAAAAAAAAAAARQnAQ')\",\r\n          }}\r\n        >\r\n          <div\r\n            style={{\r\n              fontSize: '20px',\r\n              color: token.colorTextHeading,\r\n            }}\r\n          >\r\n            欢迎使用 Ant Design Pro\r\n          </div>\r\n          <p\r\n            style={{\r\n              fontSize: '14px',\r\n              color: token.colorTextSecondary,\r\n              lineHeight: '22px',\r\n              marginTop: 16,\r\n              marginBottom: 32,\r\n              width: '65%',\r\n            }}\r\n          >\r\n            Ant Design Pro 是一个整合了 umi，Ant Design 和 ProComponents\r\n            的脚手架方案。致力于在设计规范和基础组件的基础上，继续向上构建，提炼出典型模板/业务组件/配套设计资源，进一步提升企业级中后台产品设计研发过程中的『用户』和『设计者』的体验。\r\n          </p>\r\n          <div\r\n            style={{\r\n              display: 'flex',\r\n              flexWrap: 'wrap',\r\n              gap: 16,\r\n            }}\r\n          >\r\n            <InfoCard\r\n              index={1}\r\n              href=\"https://umijs.org/docs/introduce/introduce\"\r\n              title=\"了解 umi\"\r\n              desc=\"umi 是一个可扩展的企业级前端应用框架,umi 以路由为基础的，同时支持配置式路由和约定式路由，保证路由的功能完备，并以此进行功能扩展。\"\r\n            />\r\n            <InfoCard\r\n              index={2}\r\n              title=\"了解 ant design\"\r\n              href=\"https://ant.design\"\r\n              desc=\"antd 是基于 Ant Design 设计体系的 React UI 组件库，主要用于研发企业级中后台产品。\"\r\n            />\r\n            <InfoCard\r\n              index={3}\r\n              title=\"了解 Pro Components\"\r\n              href=\"https://procomponents.ant.design\"\r\n              desc=\"ProComponents 是一个基于 Ant Design 做了更高抽象的模板组件，以 一个组件就是一个页面为开发理念，为中后台开发带来更好的体验。\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </Card>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default Welcome;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/Welcome.tsx"}, "4": {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "parentId": "ant-design-pro-layout", "id": "4", "absPath": "/admin"}, "5": {"path": "/admin", "redirect": "/admin/sub-page", "parentId": "4", "id": "5", "absPath": "/admin"}, "6": {"path": "/admin/sub-page", "name": "sub-page", "file": "@/pages/Admin.tsx", "parentId": "4", "id": "6", "absPath": "/admin/sub-page", "__content": "import { HeartTwoTone, SmileTwoTone } from '@ant-design/icons';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\nimport { useIntl } from '@umijs/max';\r\nimport { Alert, Card, Typography } from 'antd';\r\nimport React from 'react';\r\n\r\nconst Admin: React.FC = () => {\r\n  const intl = useIntl();\r\n  return (\r\n    <PageContainer\r\n      content={intl.formatMessage({\r\n        id: 'pages.admin.subPage.title',\r\n        defaultMessage: 'This page can only be viewed by admin',\r\n      })}\r\n    >\r\n      <Card>\r\n        <Alert\r\n          message={intl.formatMessage({\r\n            id: 'pages.welcome.alertMessage',\r\n            defaultMessage: 'Faster and stronger heavy-duty components have been released.',\r\n          })}\r\n          type=\"success\"\r\n          showIcon\r\n          banner\r\n          style={{\r\n            margin: -12,\r\n            marginBottom: 48,\r\n          }}\r\n        />\r\n        <Typography.Title level={2} style={{ textAlign: 'center' }}>\r\n          <SmileTwoTone /> Ant Design Pro <HeartTwoTone twoToneColor=\"#eb2f96\" /> You\r\n        </Typography.Title>\r\n      </Card>\r\n      <p style={{ textAlign: 'center', marginTop: 24 }}>\r\n        Want to add more pages? Please refer to{' '}\r\n        <a href=\"https://pro.ant.design/docs/block-cn\" target=\"_blank\" rel=\"noopener noreferrer\">\r\n          use block\r\n        </a>\r\n        。\r\n      </p>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default Admin;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/Admin.tsx"}, "7": {"name": "list.table-list", "icon": "table", "path": "/list", "file": "@/pages/TableList/index.tsx", "parentId": "ant-design-pro-layout", "id": "7", "absPath": "/list", "__content": "import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';\r\nimport { PlusOutlined } from '@ant-design/icons';\r\nimport type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';\r\nimport {\r\n  FooterToolbar,\r\n  ModalForm,\r\n  PageContainer,\r\n  ProDescriptions,\r\n  ProFormText,\r\n  ProFormTextArea,\r\n  ProTable,\r\n} from '@ant-design/pro-components';\r\nimport { FormattedMessage, useIntl } from '@umijs/max';\r\nimport { Button, Drawer, Input, message } from 'antd';\r\nimport React, { useRef, useState } from 'react';\r\nimport type { FormValueType } from './components/UpdateForm';\r\nimport UpdateForm from './components/UpdateForm';\r\n\r\n/**\r\n * @en-US Add node\r\n * @zh-CN 添加节点\r\n * @param fields\r\n */\r\nconst handleAdd = async (fields: API.RuleListItem) => {\r\n  const hide = message.loading('正在添加');\r\n  try {\r\n    await addRule({ ...fields });\r\n    hide();\r\n    message.success('Added successfully');\r\n    return true;\r\n  } catch (error) {\r\n    hide();\r\n    message.error('Adding failed, please try again!');\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * @en-US Update node\r\n * @zh-CN 更新节点\r\n *\r\n * @param fields\r\n */\r\nconst handleUpdate = async (fields: FormValueType) => {\r\n  const hide = message.loading('Configuring');\r\n  try {\r\n    await updateRule({\r\n      name: fields.name,\r\n      desc: fields.desc,\r\n      key: fields.key,\r\n    });\r\n    hide();\r\n\r\n    message.success('Configuration is successful');\r\n    return true;\r\n  } catch (error) {\r\n    hide();\r\n    message.error('Configuration failed, please try again!');\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n *  Delete node\r\n * @zh-CN 删除节点\r\n *\r\n * @param selectedRows\r\n */\r\nconst handleRemove = async (selectedRows: API.RuleListItem[]) => {\r\n  const hide = message.loading('正在删除');\r\n  if (!selectedRows) return true;\r\n  try {\r\n    await removeRule({\r\n      key: selectedRows.map((row) => row.key),\r\n    });\r\n    hide();\r\n    message.success('Deleted successfully and will refresh soon');\r\n    return true;\r\n  } catch (error) {\r\n    hide();\r\n    message.error('Delete failed, please try again');\r\n    return false;\r\n  }\r\n};\r\n\r\nconst TableList: React.FC = () => {\r\n  /**\r\n   * @en-US Pop-up window of new window\r\n   * @zh-CN 新建窗口的弹窗\r\n   *  */\r\n  const [createModalOpen, handleModalOpen] = useState<boolean>(false);\r\n  /**\r\n   * @en-US The pop-up window of the distribution update window\r\n   * @zh-CN 分布更新窗口的弹窗\r\n   * */\r\n  const [updateModalOpen, handleUpdateModalOpen] = useState<boolean>(false);\r\n\r\n  const [showDetail, setShowDetail] = useState<boolean>(false);\r\n\r\n  const actionRef = useRef<ActionType>();\r\n  const [currentRow, setCurrentRow] = useState<API.RuleListItem>();\r\n  const [selectedRowsState, setSelectedRows] = useState<API.RuleListItem[]>([]);\r\n\r\n  /**\r\n   * @en-US International configuration\r\n   * @zh-CN 国际化配置\r\n   * */\r\n  const intl = useIntl();\r\n\r\n  const columns: ProColumns<API.RuleListItem>[] = [\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.updateForm.ruleName.nameLabel\"\r\n          defaultMessage=\"Rule name\"\r\n        />\r\n      ),\r\n      dataIndex: 'name',\r\n      tip: 'The rule name is the unique key',\r\n      render: (dom, entity) => {\r\n        return (\r\n          <a\r\n            onClick={() => {\r\n              setCurrentRow(entity);\r\n              setShowDetail(true);\r\n            }}\r\n          >\r\n            {dom}\r\n          </a>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.searchTable.titleDesc\" defaultMessage=\"Description\" />,\r\n      dataIndex: 'desc',\r\n      valueType: 'textarea',\r\n    },\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.titleCallNo\"\r\n          defaultMessage=\"Number of service calls\"\r\n        />\r\n      ),\r\n      dataIndex: 'callNo',\r\n      sorter: true,\r\n      hideInForm: true,\r\n      renderText: (val: string) =>\r\n        `${val}${intl.formatMessage({\r\n          id: 'pages.searchTable.tenThousand',\r\n          defaultMessage: ' 万 ',\r\n        })}`,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.searchTable.titleStatus\" defaultMessage=\"Status\" />,\r\n      dataIndex: 'status',\r\n      hideInForm: true,\r\n      valueEnum: {\r\n        0: {\r\n          text: (\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.nameStatus.default\"\r\n              defaultMessage=\"Shut down\"\r\n            />\r\n          ),\r\n          status: 'Default',\r\n        },\r\n        1: {\r\n          text: (\r\n            <FormattedMessage id=\"pages.searchTable.nameStatus.running\" defaultMessage=\"Running\" />\r\n          ),\r\n          status: 'Processing',\r\n        },\r\n        2: {\r\n          text: (\r\n            <FormattedMessage id=\"pages.searchTable.nameStatus.online\" defaultMessage=\"Online\" />\r\n          ),\r\n          status: 'Success',\r\n        },\r\n        3: {\r\n          text: (\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.nameStatus.abnormal\"\r\n              defaultMessage=\"Abnormal\"\r\n            />\r\n          ),\r\n          status: 'Error',\r\n        },\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.titleUpdatedAt\"\r\n          defaultMessage=\"Last scheduled time\"\r\n        />\r\n      ),\r\n      sorter: true,\r\n      dataIndex: 'updatedAt',\r\n      valueType: 'dateTime',\r\n      renderFormItem: (item, { defaultRender, ...rest }, form) => {\r\n        const status = form.getFieldValue('status');\r\n        if (`${status}` === '0') {\r\n          return false;\r\n        }\r\n        if (`${status}` === '3') {\r\n          return (\r\n            <Input\r\n              {...rest}\r\n              placeholder={intl.formatMessage({\r\n                id: 'pages.searchTable.exception',\r\n                defaultMessage: 'Please enter the reason for the exception!',\r\n              })}\r\n            />\r\n          );\r\n        }\r\n        return defaultRender(item);\r\n      },\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.searchTable.titleOption\" defaultMessage=\"Operating\" />,\r\n      dataIndex: 'option',\r\n      valueType: 'option',\r\n      render: (_, record) => [\r\n        <a\r\n          key=\"config\"\r\n          onClick={() => {\r\n            handleUpdateModalOpen(true);\r\n            setCurrentRow(record);\r\n          }}\r\n        >\r\n          <FormattedMessage id=\"pages.searchTable.config\" defaultMessage=\"Configuration\" />\r\n        </a>,\r\n        <a key=\"subscribeAlert\" href=\"https://procomponents.ant.design/\">\r\n          <FormattedMessage\r\n            id=\"pages.searchTable.subscribeAlert\"\r\n            defaultMessage=\"Subscribe to alerts\"\r\n          />\r\n        </a>,\r\n      ],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <PageContainer>\r\n      <ProTable<API.RuleListItem, API.PageParams>\r\n        headerTitle={intl.formatMessage({\r\n          id: 'pages.searchTable.title',\r\n          defaultMessage: 'Enquiry form',\r\n        })}\r\n        actionRef={actionRef}\r\n        rowKey=\"key\"\r\n        search={{\r\n          labelWidth: 120,\r\n        }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            onClick={() => {\r\n              handleModalOpen(true);\r\n            }}\r\n          >\r\n            <PlusOutlined /> <FormattedMessage id=\"pages.searchTable.new\" defaultMessage=\"New\" />\r\n          </Button>,\r\n        ]}\r\n        request={rule}\r\n        columns={columns}\r\n        rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }}\r\n      />\r\n      {selectedRowsState?.length > 0 && (\r\n        <FooterToolbar\r\n          extra={\r\n            <div>\r\n              <FormattedMessage id=\"pages.searchTable.chosen\" defaultMessage=\"Chosen\" />{' '}\r\n              <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>{' '}\r\n              <FormattedMessage id=\"pages.searchTable.item\" defaultMessage=\"项\" />\r\n              &nbsp;&nbsp;\r\n              <span>\r\n                <FormattedMessage\r\n                  id=\"pages.searchTable.totalServiceCalls\"\r\n                  defaultMessage=\"Total number of service calls\"\r\n                />{' '}\r\n                {selectedRowsState.reduce((pre, item) => pre + item.callNo!, 0)}{' '}\r\n                <FormattedMessage id=\"pages.searchTable.tenThousand\" defaultMessage=\"万\" />\r\n              </span>\r\n            </div>\r\n          }\r\n        >\r\n          <Button\r\n            onClick={async () => {\r\n              await handleRemove(selectedRowsState);\r\n              setSelectedRows([]);\r\n              actionRef.current?.reloadAndRest?.();\r\n            }}\r\n          >\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.batchDeletion\"\r\n              defaultMessage=\"Batch deletion\"\r\n            />\r\n          </Button>\r\n          <Button type=\"primary\">\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.batchApproval\"\r\n              defaultMessage=\"Batch approval\"\r\n            />\r\n          </Button>\r\n        </FooterToolbar>\r\n      )}\r\n      <ModalForm\r\n        title={intl.formatMessage({\r\n          id: 'pages.searchTable.createForm.newRule',\r\n          defaultMessage: 'New rule',\r\n        })}\r\n        width=\"400px\"\r\n        open={createModalOpen}\r\n        onOpenChange={handleModalOpen}\r\n        onFinish={async (value) => {\r\n          const success = await handleAdd(value as API.RuleListItem);\r\n          if (success) {\r\n            handleModalOpen(false);\r\n            if (actionRef.current) {\r\n              actionRef.current.reload();\r\n            }\r\n          }\r\n        }}\r\n      >\r\n        <ProFormText\r\n          rules={[\r\n            {\r\n              required: true,\r\n              message: (\r\n                <FormattedMessage\r\n                  id=\"pages.searchTable.ruleName\"\r\n                  defaultMessage=\"Rule name is required\"\r\n                />\r\n              ),\r\n            },\r\n          ]}\r\n          width=\"md\"\r\n          name=\"name\"\r\n        />\r\n        <ProFormTextArea width=\"md\" name=\"desc\" />\r\n      </ModalForm>\r\n      <UpdateForm\r\n        onSubmit={async (value) => {\r\n          const success = await handleUpdate(value);\r\n          if (success) {\r\n            handleUpdateModalOpen(false);\r\n            setCurrentRow(undefined);\r\n            if (actionRef.current) {\r\n              actionRef.current.reload();\r\n            }\r\n          }\r\n        }}\r\n        onCancel={() => {\r\n          handleUpdateModalOpen(false);\r\n          if (!showDetail) {\r\n            setCurrentRow(undefined);\r\n          }\r\n        }}\r\n        updateModalOpen={updateModalOpen}\r\n        values={currentRow || {}}\r\n      />\r\n\r\n      <Drawer\r\n        width={600}\r\n        open={showDetail}\r\n        onClose={() => {\r\n          setCurrentRow(undefined);\r\n          setShowDetail(false);\r\n        }}\r\n        closable={false}\r\n      >\r\n        {currentRow?.name && (\r\n          <ProDescriptions<API.RuleListItem>\r\n            column={2}\r\n            title={currentRow?.name}\r\n            request={async () => ({\r\n              data: currentRow || {},\r\n            })}\r\n            params={{\r\n              id: currentRow?.name,\r\n            }}\r\n            columns={columns as ProDescriptionsItemProps<API.RuleListItem>[]}\r\n          />\r\n        )}\r\n      </Drawer>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default TableList;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/TableList/index.tsx"}, "8": {"path": "/product", "name": "product", "icon": "table", "parentId": "ant-design-pro-layout", "id": "8", "absPath": "/product"}, "9": {"path": "/product/list", "name": "productList", "file": "@/pages/ProductList/index.tsx", "parentId": "8", "id": "9", "absPath": "/product/list", "__content": "import React, { useRef, useState } from 'react';\nimport { ProTable, ProColumns, ActionType, ModalForm, ProFormText, ProFormTextArea, ProFormDigit } from '@ant-design/pro-components';\nimport { createProduct, updateProduct, deleteProduct, searchProducts } from '@/services/swagger/productController';\nimport { Button, message, Popconfirm } from 'antd';\nimport { PlusOutlined } from '@ant-design/icons';\n\nconst ProductListPage: React.FC = () => {\n  const actionRef = useRef<ActionType>();\n  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\n  const [currentRow, setCurrentRow] = useState<API.Product | undefined>(undefined);\n\n  const handleAdd = async (fields: API.Product) => {\n    const hide = message.loading('正在添加');\n    try {\n      await createProduct({ ...fields });\n      hide();\n      message.success('添加成功');\n      return true;\n    } catch (error) {\n      hide();\n      message.error('添加失败，请重试！');\n      return false;\n    }\n  };\n\n  const handleUpdate = async (fields: API.Product) => {\n    const hide = message.loading('正在配置');\n    try {\n      await updateProduct({ id: currentRow?.id as number }, { ...fields });\n      hide();\n      message.success('配置成功');\n      return true;\n    } catch (error) {\n      hide();\n      message.error('配置失败，请重试！');\n      return false;\n    }\n  };\n\n  const handleDelete = async (id: number) => {\n    const hide = message.loading('正在删除');\n    try {\n      await deleteProduct({ id });\n      hide();\n      message.success('删除成功');\n      if (actionRef.current) {\n        actionRef.current.reload();\n      }\n      return true;\n    } catch (error) {\n      hide();\n      message.error('删除失败，请重试！');\n      return false;\n    }\n  };\n\n  const columns: ProColumns<API.Product>[] = [\n    {\n      title: '商品ID',\n      dataIndex: 'id',\n      valueType: 'digit',\n      hideInSearch: true,\n      editable: false, // ID不可编辑\n    },\n    {\n      title: '商品名或描述',\n      dataIndex: 'name',\n      valueType: 'text',\n      formItemProps: {\n        rules: [\n          {\n            required: false, // 搜索字段可以为空\n          },\n        ],\n      },\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      valueType: 'textarea',\n      hideInSearch: true,\n    },\n    {\n      title: '价格',\n      dataIndex: 'price',\n      valueType: 'money',\n      hideInSearch: true,\n      sorter: true, // 启用价格排序\n    },\n    {\n      title: '库存',\n      dataIndex: 'stock',\n      valueType: 'digit',\n      hideInSearch: true,\n    },\n    {\n      title: '分类ID',\n      dataIndex: 'categoryId',\n      valueType: 'digit',\n      hideInSearch: true,\n    },\n    {\n      title: '商品图片',\n      dataIndex: 'imageUrl',\n      valueType: 'image',\n      hideInSearch: true,\n      render: (_, record) => (record.imageUrl ? <img src={record.imageUrl} alt=\"商品图片\" style={{ width: 50, height: 50 }} /> : null),\n    },\n    {\n      title: '标签',\n      dataIndex: 'tags',\n      valueType: 'text',\n      hideInSearch: true,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      valueType: 'dateTime',\n      hideInSearch: true,\n      sorter: true,\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      valueType: 'dateTime',\n      hideInSearch: true,\n      sorter: true,\n    },\n    {\n      title: '操作',\n      valueType: 'option',\n      key: 'option',\n      render: (_, record) => [\n        <a\n          key=\"edit\"\n          onClick={() => {\n            handleUpdateModalVisible(true);\n            setCurrentRow(record);\n          }}\n        >\n          编辑\n        </a>,\n        <Popconfirm\n          key=\"delete\"\n          title=\"确定删除该商品吗？\"\n          onConfirm={async () => {\n            await handleDelete(record.id as number);\n          }}\n          okText=\"是\"\n          cancelText=\"否\"\n        >\n          <a>删除</a>\n        </Popconfirm>,\n      ],\n    },\n  ];\n\n  return (\n    <>\n      <ProTable<API.Product>\n        headerTitle=\"商品列表\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        request={async (params, sorter) => {\n          const searchParams: API.searchProductsParams = {\n            keyword: params.name || '',\n          };\n          const msg = await searchProducts(searchParams);\n\n          let sortedData = [...msg]; // 复制一份数据进行排序\n\n          // 前端排序逻辑\n          if (Object.keys(sorter).length > 0) {\n            const sortField = Object.keys(sorter)[0];\n            const sortOrder = sorter[sortField];\n\n            sortedData.sort((a, b) => {\n              const aValue = a[sortField as keyof API.Product];\n              const bValue = b[sortField as keyof API.Product];\n\n              if (typeof aValue === 'string' && typeof bValue === 'string') {\n                return sortOrder === 'ascend' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n              }\n              if (typeof aValue === 'number' && typeof bValue === 'number') {\n                return sortOrder === 'ascend' ? aValue - bValue : bValue - aValue;\n              }\n              // 对于日期类型，可以转换为时间戳进行比较\n              if (sortField === 'createdAt' || sortField === 'updatedAt') {\n                const aTime = new Date(aValue as string).getTime();\n                const bTime = new Date(bValue as string).getTime();\n                return sortOrder === 'ascend' ? aTime - bTime : bTime - aTime;\n              }\n              return 0;\n            });\n          }\n\n          return {\n            data: sortedData,\n            success: true,\n            total: sortedData.length,\n          };\n        }}\n        columns={columns}\n        search={{\n          labelWidth: 'auto',\n        }}\n        options={{\n          setting: {\n            listsHeight: 400,\n          },\n        }}\n        toolBarRender={() => [\n          <Button\n            key=\"button\"\n            icon={<PlusOutlined />}\n            type=\"primary\"\n            onClick={() => handleCreateModalVisible(true)}\n          >\n            新建商品\n          </Button>,\n        ]}\n      />\n\n      <ModalForm\n        title=\"新建商品\"\n        width=\"400px\"\n        open={createModalVisible}\n        onOpenChange={handleCreateModalVisible}\n        onFinish={async (value) => {\n          const success = await handleAdd(value as API.Product);\n          if (success) {\n            handleCreateModalVisible(false);\n            if (actionRef.current) {\n              actionRef.current.reload();\n            }\n          }\n        }}\n      >\n        <ProFormText\n          rules={[\n            {\n              required: true,\n              message: '商品名称为必填项',\n            },\n          ]}\n          width=\"md\"\n          name=\"name\"\n          label=\"商品名称\"\n        />\n        <ProFormTextArea width=\"md\" name=\"description\" label=\"描述\" />\n        <ProFormDigit\n          rules={[\n            {\n              required: true,\n              message: '价格为必填项',\n            },\n          ]}\n          width=\"md\"\n          name=\"price\"\n          label=\"价格\"\n          min={0}\n          fieldProps={{ precision: 2 }}\n        />\n        <ProFormDigit\n          rules={[\n            {\n              required: true,\n              message: '库存为必填项',\n            },\n          ]}\n          width=\"md\"\n          name=\"stock\"\n          label=\"库存\"\n          min={0}\n        />\n        <ProFormDigit width=\"md\" name=\"categoryId\" label=\"分类ID\" min={0} />\n        <ProFormText width=\"md\" name=\"imageUrl\" label=\"图片URL\" />\n        <ProFormText width=\"md\" name=\"tags\" label=\"标签\" />\n      </ModalForm>\n\n      <ModalForm\n        title=\"编辑商品\"\n        width=\"400px\"\n        open={updateModalVisible}\n        onOpenChange={handleUpdateModalVisible}\n        initialValues={currentRow}\n        onFinish={async (value) => {\n          const success = await handleUpdate(value as API.Product);\n          if (success) {\n            handleUpdateModalVisible(false);\n            setCurrentRow(undefined);\n            if (actionRef.current) {\n              actionRef.current.reload();\n            }\n          }\n        }}\n      >\n        <ProFormText\n          rules={[\n            {\n              required: true,\n              message: '商品名称为必填项',\n            },\n          ]}\n          width=\"md\"\n          name=\"name\"\n          label=\"商品名称\"\n        />\n        <ProFormTextArea width=\"md\" name=\"description\" label=\"描述\" />\n        <ProFormDigit\n          rules={[\n            {\n              required: true,\n              message: '价格为必填项',\n            },\n          ]}\n          width=\"md\"\n          name=\"price\"\n          label=\"价格\"\n          min={0}\n          fieldProps={{ precision: 2 }}\n        />\n        <ProFormDigit\n          rules={[\n            {\n              required: true,\n              message: '库存为必填项',\n            },\n          ]}\n          width=\"md\"\n          name=\"stock\"\n          label=\"库存\"\n          min={0}\n        />\n        <ProFormDigit width=\"md\" name=\"categoryId\" label=\"分类ID\" min={0} />\n        <ProFormText width=\"md\" name=\"imageUrl\" label=\"图片URL\" />\n        <ProFormText width=\"md\" name=\"tags\" label=\"标签\" />\n      </ModalForm>\n    </>\n  );\n};\n\nexport default ProductListPage;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/ProductList/index.tsx"}, "10": {"path": "/", "redirect": "/welcome", "parentId": "ant-design-pro-layout", "id": "10", "absPath": "/"}, "11": {"path": "/order", "name": "order", "icon": "shoppingCart", "parentId": "ant-design-pro-layout", "id": "11", "absPath": "/order"}, "12": {"path": "/order/list", "name": "list", "file": "@/pages/OrderList/index.tsx", "parentId": "11", "id": "12", "absPath": "/order/list", "__content": "import { PageContainer, ProTable, ProColumns } from '@ant-design/pro-components';\nimport { message, Popconfirm, Button } from 'antd';\nimport React, { useRef } from 'react';\nimport API from '@/services/ant-design-pro/api';\nimport { history } from '@umijs/max';\n\nconst OrderList: React.FC = () => {\n  const actionRef = useRef<any>();\n\n  const handleRemove = async (id: number) => {\n    try {\n      await API.Order.cancelOrder({ id });\n      message.success('删除成功');\n      actionRef.current?.reload();\n    } catch (error) {\n      message.error('删除失败，请重试');\n    }\n  };\n\n  const columns: ProColumns<API.Order>[] = [\n    {\n      title: '订单ID',\n      dataIndex: 'id',\n      valueType: 'text',\n      search: false,\n      render: (text, record) => (\n        <a onClick={() => history.push(`/order/detail/${record.id}`)}>{text}</a>\n      ),\n    },\n    {\n      title: '用户名',\n      dataIndex: 'userName',\n      valueType: 'text',\n      search: false,\n    },\n    {\n      title: '总价',\n      dataIndex: 'totalAmount',\n      valueType: 'money',\n      search: false,\n    },\n    {\n      title: '状态',\n      dataIndex: ['status', 'statusName'],\n      valueType: 'text',\n      search: false,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      valueType: 'dateTime',\n      search: false,\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      valueType: 'dateTime',\n      search: false,\n    },\n    {\n      title: '商品详情',\n      dataIndex: 'products',\n      valueType: 'text',\n      search: false,\n      render: (_, record) => (\n        <div>\n          {record.products?.map((product, index) => (\n            <div key={index}>\n              {product.name} - {product.price} x {product.quantity}\n            </div>\n          ))}\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      dataIndex: 'option',\n      valueType: 'option',\n      render: (_: any, record: API.Order) => [\n        <Popconfirm\n          key=\"delete\"\n          title=\"确定删除该订单吗？\"\n          onConfirm={() => handleRemove(record.id!)}\n          okText=\"是\"\n          cancelText=\"否\"\n        >\n          <Button type=\"link\" danger>\n            删除\n          </Button>\n        </Popconfirm>,\n      ],\n    },\n  ];\n\n\n\n  return (\n    <PageContainer>\n      <ProTable<API.Order>\n        headerTitle=\"订单列表\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        request={async (params) => {\n          const allOrders = await API.Order.getAllOrders(); // 获取所有订单数据\n\n          const { current, pageSize } = params;\n          const startIndex = (current! - 1) * pageSize!;\n          const endIndex = startIndex + pageSize!;\n          const paginatedData = allOrders.slice(startIndex, endIndex);\n\n          return {\n            data: paginatedData,\n            success: true,\n            total: allOrders.length,\n          };\n        }}\n        columns={columns}\n        pagination={{\n          defaultPageSize: 10, // 设置默认分页大小\n          showSizeChanger: true,\n        }}\n        toolBarRender={() => []}\n      />\n    </PageContainer>\n  );\n};\n\nexport default OrderList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/OrderList/index.tsx"}, "13": {"path": "/order/detail/:id", "name": "detail", "hideInMenu": true, "file": "@/pages/OrderDetail/index.tsx", "parentId": "11", "id": "13", "absPath": "/order/detail/:id", "__content": "import React from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Descriptions, Spin, message } from 'antd';\nimport { useParams } from '@umijs/max';\nimport { useEffect, useState } from 'react';\nimport API from '@/services/ant-design-pro/api';\n\nconst OrderDetail: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const [orderDetail, setOrderDetail] = useState<API.Order | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n\n  useEffect(() => {\n    const fetchOrderDetail = async () => {\n      try {\n        setLoading(true);\n        const response = await API.Order.getOrderById({ id: parseInt(id!) });\n        setOrderDetail(response);\n      } catch (error) {\n        message.error('获取订单详情失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id) {\n      fetchOrderDetail();\n    }\n  }, [id]);\n\n  if (loading) {\n    return <Spin tip=\"加载中...\" />;\n  }\n\n  if (!orderDetail) {\n    return <PageContainer>订单详情加载失败或订单不存在</PageContainer>;\n  }\n\n  return (\n    <PageContainer header={{ title: `订单详情 - ${orderDetail.id}` }}>\n      <Card title=\"基本信息\">\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"订单ID\">{orderDetail.id}</Descriptions.Item>\n          <Descriptions.Item label=\"用户名\">{orderDetail.userName}</Descriptions.Item>\n          <Descriptions.Item label=\"总价\">{orderDetail.totalAmount}</Descriptions.Item>\n          <Descriptions.Item label=\"状态\">{orderDetail.status?.statusName}</Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">{orderDetail.createdAt}</Descriptions.Item>\n          <Descriptions.Item label=\"更新时间\">{orderDetail.updatedAt}</Descriptions.Item>\n        </Descriptions>\n      </Card>\n      <Card title=\"商品列表\" style={{ marginTop: 16 }}>\n        <Descriptions column={1}>\n          {orderDetail.orderItems?.map((item, index) => (\n            <Descriptions.Item key={index} label={`商品 ${index + 1}`}>\n              {item.productNameAtPurchase} - 单价: {item.price} - 数量: {item.quantity}\n            </Descriptions.Item>\n          ))}\n        </Descriptions>\n      </Card>\n      {/* 可以在这里添加更多详情，如物流信息、支付信息等 */}\n    </PageContainer>\n  );\n};\n\nexport default OrderDetail;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/OrderDetail/index.tsx"}, "14": {"path": "*", "layout": false, "file": "@/pages/404.tsx", "id": "14", "absPath": "/*", "__content": "import { history, useIntl } from '@umijs/max';\r\nimport { Button, Result } from 'antd';\r\nimport React from 'react';\r\n\r\nconst NoFoundPage: React.FC = () => (\r\n  <Result\r\n    status=\"404\"\r\n    title=\"404\"\r\n    subTitle={useIntl().formatMessage({ id: 'pages.404.subTitle' })}\r\n    extra={\r\n      <Button type=\"primary\" onClick={() => history.push('/')}>\r\n        {useIntl().formatMessage({ id: 'pages.404.buttonText' })}\r\n      </Button>\r\n    }\r\n  />\r\n);\r\n\r\nexport default NoFoundPage;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/404.tsx"}, "ant-design-pro-layout": {"id": "ant-design-pro-layout", "path": "/", "file": "C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi/plugin-layout/Layout.tsx", "absPath": "/", "isLayout": true, "__absFile": "C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi/plugin-layout/Layout.tsx"}, "umi/plugin/openapi": {"path": "/umi/plugin/openapi", "absPath": "/umi/plugin/openapi", "id": "umi/plugin/openapi", "file": "C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi/plugin-openapi/openapi.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "pnpm", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "mfsuBundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\react"}, "react-dom": {"version": "18.3.1", "path": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\react-dom"}, "appJS": {"path": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\app.tsx", "exports": ["getInitialState", "layout", "request"]}, "locale": "zh-CN", "globalCSS": ["C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\global.less"], "globalJS": ["C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\global.tsx"], "overridesCSS": [], "bundler": "webpack", "git": {"originUrl": "https://github.com/roshad/mall-admin.git"}, "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 8000, "host": "0.0.0.0", "ip": "**********", "antd": {"pkgPath": "C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\antd", "version": "5.25.3"}, "pluginLayout": {"pkgPath": "C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/@ant-design/pro-components", "version": "2.8.7"}}